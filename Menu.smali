.class public Lcom/android/support/Menu;
.super Ljava/lang/Object;
.source "Menu.java"


# static fields
.field public static final TAG:Ljava/lang/String; = "Mod_Menu"


# instance fields
.field BTN_COLOR:I

.field BtnOFF:I

.field BtnON:I

.field CategoryBG:I

.field CheckBoxColor:I

.field ICON_ALPHA:F

.field ICON_SIZE:I

.field MENU_BG_COLOR:I

.field MENU_CORNER:F

.field MENU_FEATURE_BG_COLOR:I

.field MENU_HEIGHT:I

.field MENU_WIDTH:I

.field NumberTxtColor:Ljava/lang/String;

.field POS_X:I

.field POS_Y:I

.field RadioColor:I

.field SeekBarColor:I

.field SeekBarProgressColor:I

.field TEXT_COLOR:I

.field TEXT_COLOR_2:I

.field TEXT_COLOR_3:I

.field TEXT_COLOR_4:I

.field TEXT_COLOR_5:I

.field TEXT_COLOR_6:I

.field ToggleOFF:I

.field ToggleON:I

.field categorymaintext:Landroid/widget/TextView;

.field categorytitle:Landroid/widget/TextView;

.field getContext:Landroid/content/Context;

.field icon_back:Landroid/widget/TextView;

.field mCollapse:Landroid/widget/LinearLayout;

.field mCollapsed:Landroid/widget/RelativeLayout;

.field mExpanded:Landroid/widget/LinearLayout;

.field mRootContainer:Landroid/widget/RelativeLayout;

.field mSettings:Landroid/widget/LinearLayout;

.field mWindowManager:Landroid/view/WindowManager;

.field mods:Landroid/widget/LinearLayout;

.field overlayRequired:Z

.field rootFrame:Landroid/widget/FrameLayout;

.field scrlLL:Landroid/widget/LinearLayout$LayoutParams;

.field scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

.field scrollView:Landroid/widget/ScrollView;

.field startimage:Landroid/widget/ImageView;

.field stopChecking:Z

.field title:Landroid/widget/TextView;

.field vmParams:Landroid/view/WindowManager$LayoutParams;


# direct methods
.method static bridge synthetic -$$Nest$mButton(Lcom/android/support/Menu;Landroid/widget/LinearLayout;ILjava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/android/support/Menu;->Button(Landroid/widget/LinearLayout;ILjava/lang/String;)V

    return-void
.end method

.method static bridge synthetic -$$Nest$mfeatureList(Lcom/android/support/Menu;[Ljava/lang/String;Landroid/widget/LinearLayout;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/android/support/Menu;->featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V

    return-void
.end method

.method static bridge synthetic -$$Nest$mfromHtml(Lcom/android/support/Menu;Ljava/lang/String;)Ljava/lang/CharSequence;
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object p0

    return-object p0
.end method

.method static bridge synthetic -$$Nest$misViewCollapsed(Lcom/android/support/Menu;)Z
    .locals 0

    invoke-direct {p0}, Lcom/android/support/Menu;->isViewCollapsed()Z

    move-result p0

    return p0
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;

    .line 165
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 91
    const-string v0, "#82CAFD"

    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/android/support/Menu;->TEXT_COLOR:I

    .line 92
    const-string v0, "#FFFFFF"

    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    .line 93
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_3:I

    .line 94
    const-string v1, "#9932cc"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_4:I

    .line 95
    const-string v1, "#482d55"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_5:I

    .line 96
    const-string v1, "#000000"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_6:I

    .line 97
    const-string v1, "#1C262D"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BTN_COLOR:I

    .line 98
    const-string v1, "#EE1C2A35"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    .line 99
    const-string v1, "#DD141C22"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->MENU_FEATURE_BG_COLOR:I

    .line 100
    const/16 v1, 0x122

    iput v1, p0, Lcom/android/support/Menu;->MENU_WIDTH:I

    .line 101
    const/16 v1, 0xd2

    iput v1, p0, Lcom/android/support/Menu;->MENU_HEIGHT:I

    .line 102
    const/4 v1, 0x0

    iput v1, p0, Lcom/android/support/Menu;->POS_X:I

    .line 103
    const/16 v1, 0x64

    iput v1, p0, Lcom/android/support/Menu;->POS_Y:I

    .line 104
    const/high16 v1, 0x40800000    # 4.0f

    iput v1, p0, Lcom/android/support/Menu;->MENU_CORNER:F

    .line 105
    const/16 v1, 0x2d

    iput v1, p0, Lcom/android/support/Menu;->ICON_SIZE:I

    .line 106
    const v1, 0x3f333333    # 0.7f

    iput v1, p0, Lcom/android/support/Menu;->ICON_ALPHA:F

    .line 107
    const v1, -0xff0100

    iput v1, p0, Lcom/android/support/Menu;->ToggleON:I

    .line 108
    const/high16 v1, -0x10000

    iput v1, p0, Lcom/android/support/Menu;->ToggleOFF:I

    .line 109
    const-string v1, "#1b5e20"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BtnON:I

    .line 110
    const-string v1, "#7f0000"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BtnOFF:I

    .line 111
    const-string v1, "#2F3D4C"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->CategoryBG:I

    .line 112
    const-string v1, "#80CBC4"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, p0, Lcom/android/support/Menu;->SeekBarColor:I

    .line 113
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, p0, Lcom/android/support/Menu;->SeekBarProgressColor:I

    .line 114
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->CheckBoxColor:I

    .line 115
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/android/support/Menu;->RadioColor:I

    .line 116
    const-string v0, "#41c300"

    iput-object v0, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    .line 166
    invoke-direct {p0, p1}, Lcom/android/support/Menu;->initializeMenu(Landroid/content/Context;)V

    .line 167
    return-void
.end method

.method private AddColor(Landroid/view/View;IIIIIIIIIIIII)V
    .locals 17
    .param p1, "view"    # Landroid/view/View;
    .param p2, "color"    # I
    .param p3, "strokeWidth"    # I
    .param p4, "dashWidth"    # I
    .param p5, "dashGap"    # I
    .param p6, "strokeColor"    # I
    .param p7, "r1"    # I
    .param p8, "r2"    # I
    .param p9, "r3"    # I
    .param p10, "r4"    # I
    .param p11, "r5"    # I
    .param p12, "r6"    # I
    .param p13, "r7"    # I
    .param p14, "r8"    # I

    .line 145
    new-instance v0, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v0}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 146
    .local v0, "gd":Landroid/graphics/drawable/GradientDrawable;
    move/from16 v1, p2

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 147
    move/from16 v2, p7

    int-to-float v3, v2

    move/from16 v4, p8

    int-to-float v5, v4

    move/from16 v6, p9

    int-to-float v7, v6

    move/from16 v8, p10

    int-to-float v9, v8

    move/from16 v10, p11

    int-to-float v11, v10

    move/from16 v12, p12

    int-to-float v13, v12

    move/from16 v14, p13

    int-to-float v15, v14

    move/from16 v1, p14

    int-to-float v2, v1

    const/16 v1, 0x8

    new-array v1, v1, [F

    const/16 v16, 0x0

    aput v3, v1, v16

    const/4 v3, 0x1

    aput v5, v1, v3

    const/4 v3, 0x2

    aput v7, v1, v3

    const/4 v3, 0x3

    aput v9, v1, v3

    const/4 v3, 0x4

    aput v11, v1, v3

    const/4 v3, 0x5

    aput v13, v1, v3

    const/4 v3, 0x6

    aput v15, v1, v3

    const/4 v3, 0x7

    aput v2, v1, v3

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadii([F)V

    .line 148
    move/from16 v1, p4

    int-to-float v2, v1

    move/from16 v3, p5

    int-to-float v5, v3

    move/from16 v7, p3

    move/from16 v9, p6

    invoke-virtual {v0, v7, v9, v2, v5}, Landroid/graphics/drawable/GradientDrawable;->setStroke(IIFF)V

    .line 149
    move-object/from16 v2, p1

    invoke-virtual {v2, v0}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 150
    return-void
.end method

.method private Button(Landroid/widget/LinearLayout;ILjava/lang/String;)V
    .locals 17
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;

    .line 696
    move-object/from16 v0, p0

    move-object/from16 v15, p3

    new-instance v1, Landroid/widget/Button;

    iget-object v2, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 697
    .local v1, "button":Landroid/widget/Button;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x1

    invoke-direct {v2, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 698
    .local v2, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v3, 0x7

    const/4 v4, 0x5

    invoke-virtual {v2, v3, v4, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 699
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 700
    iget v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v1, v3}, Landroid/widget/Button;->setTextColor(I)V

    .line 701
    const/4 v3, 0x0

    invoke-virtual {v1, v3}, Landroid/widget/Button;->setAllCaps(Z)V

    .line 702
    invoke-direct {v0, v15}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v3

    invoke-virtual {v1, v3}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 703
    move-object v3, v2

    .end local v2    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v3, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    iget v2, v0, Lcom/android/support/Menu;->BTN_COLOR:I

    const/16 v13, 0x1e

    const/16 v14, 0x1e

    move-object v4, v3

    .end local v3    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v4, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v3, 0x0

    move-object v5, v4

    .end local v4    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v5, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v4, 0x0

    move-object v6, v5

    .end local v5    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v6, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v5, 0x0

    move-object v7, v6

    .end local v6    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v7, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v6, 0x0

    move-object v8, v7

    .end local v7    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v8, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v7, 0x1e

    move-object v9, v8

    .end local v8    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v9, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v8, 0x1e

    move-object v10, v9

    .end local v9    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v10, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v9, 0x1e

    move-object v11, v10

    .end local v10    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v11, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v10, 0x1e

    move-object v12, v11

    .end local v11    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v12, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v11, 0x1e

    move-object/from16 v16, v12

    .end local v12    # "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    .local v16, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/16 v12, 0x1e

    invoke-direct/range {v0 .. v14}, Lcom/android/support/Menu;->AddColor(Landroid/view/View;IIIIIIIIIIIII)V

    .line 705
    new-instance v2, Lcom/android/support/Menu$10;

    move/from16 v3, p2

    invoke-direct {v2, v0, v3, v15}, Lcom/android/support/Menu$10;-><init>(Lcom/android/support/Menu;ILjava/lang/String;)V

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 721
    move-object/from16 v2, p1

    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 722
    return-void
.end method

.method private ButtonLink(Ljava/lang/String;Ljava/lang/String;)Landroid/view/View;
    .locals 16
    .param p1, "featName"    # Ljava/lang/String;
    .param p2, "url"    # Ljava/lang/String;

    .line 725
    move-object/from16 v0, p0

    new-instance v1, Landroid/widget/Button;

    iget-object v2, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 726
    .local v1, "button":Landroid/widget/Button;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x1

    invoke-direct {v2, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object v15, v2

    .line 727
    .local v15, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v2, 0x7

    const/4 v3, 0x5

    invoke-virtual {v15, v2, v3, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 728
    invoke-virtual {v1, v15}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 729
    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setAllCaps(Z)V

    .line 730
    iget v2, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setTextColor(I)V

    .line 731
    invoke-direct/range {p0 .. p1}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 732
    iget v2, v0, Lcom/android/support/Menu;->BTN_COLOR:I

    const/16 v13, 0x1e

    const/16 v14, 0x1e

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/16 v7, 0x1e

    const/16 v8, 0x1e

    const/16 v9, 0x1e

    const/16 v10, 0x1e

    const/16 v11, 0x1e

    const/16 v12, 0x1e

    invoke-direct/range {v0 .. v14}, Lcom/android/support/Menu;->AddColor(Landroid/view/View;IIIIIIIIIIIII)V

    .line 733
    new-instance v2, Lcom/android/support/Menu$11;

    move-object/from16 v3, p2

    invoke-direct {v2, v0, v3}, Lcom/android/support/Menu$11;-><init>(Lcom/android/support/Menu;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 741
    return-object v1
.end method

.method private ButtonOnOff(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
    .locals 8
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "switchedOn"    # Z

    .line 745
    new-instance v0, Landroid/widget/Button;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    move-object v7, v0

    .line 746
    .local v7, "button":Landroid/widget/Button;
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v1, -0x1

    invoke-direct {v0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 747
    .local v0, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v1, 0x7

    const/4 v2, 0x5

    invoke-virtual {v0, v1, v2, v1, v2}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 748
    invoke-virtual {v7, v0}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 749
    iget v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v7, v1}, Landroid/widget/Button;->setTextColor(I)V

    .line 750
    const/4 v1, 0x0

    invoke-virtual {v7, v1}, Landroid/widget/Button;->setAllCaps(Z)V

    .line 752
    const-string v1, "OnOff_"

    const-string v2, ""

    invoke-virtual {p3, v1, v2}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v5

    .line 753
    .local v5, "finalfeatName":Ljava/lang/String;
    invoke-static {p3, p2, p4}, Lcom/android/support/Preferences;->loadPrefBool(Ljava/lang/String;IZ)Z

    move-result v1

    .line 754
    .local v1, "isOn":Z
    if-eqz v1, :cond_0

    .line 755
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ": ON"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v2

    invoke-virtual {v7, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 756
    iget v2, p0, Lcom/android/support/Menu;->BtnON:I

    invoke-virtual {v7, v2}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 757
    const/4 v1, 0x0

    goto :goto_0

    .line 759
    :cond_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ": OFF"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v2

    invoke-virtual {v7, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 760
    iget v2, p0, Lcom/android/support/Menu;->BtnOFF:I

    invoke-virtual {v7, v2}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 761
    const/4 v1, 0x1

    .line 763
    :goto_0
    move v4, v1

    .line 764
    .local v4, "finalIsOn":Z
    new-instance v2, Lcom/android/support/Menu$12;

    move-object v3, p0

    move v6, p2

    .end local p2    # "featNum":I
    .local v6, "featNum":I
    invoke-direct/range {v2 .. v7}, Lcom/android/support/Menu$12;-><init>(Lcom/android/support/Menu;ZLjava/lang/String;ILandroid/widget/Button;)V

    invoke-virtual {v7, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 781
    invoke-virtual {p1, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 782
    return-void
.end method

.method private Category(Landroid/widget/LinearLayout;ZLjava/lang/String;)V
    .locals 5
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "z"    # Z
    .param p3, "text"    # Ljava/lang/String;

    .line 1095
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v1, -0x1

    const/4 v2, -0x2

    invoke-direct {v0, v1, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 1096
    .local v0, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v1, 0x5

    invoke-direct {p0, v1}, Lcom/android/support/Menu;->dp(I)I

    move-result v2

    const/4 v3, 0x2

    invoke-direct {p0, v3}, Lcom/android/support/Menu;->dp(I)I

    move-result v3

    invoke-direct {p0, v1}, Lcom/android/support/Menu;->dp(I)I

    move-result v1

    const/4 v4, 0x0

    invoke-virtual {v0, v2, v3, v1, v4}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 1097
    new-instance v1, Landroid/widget/TextView;

    iget-object v2, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 1098
    .local v1, "textView":Landroid/widget/TextView;
    invoke-direct {p0, p3}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1099
    const/16 v2, 0x11

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setGravity(I)V

    .line 1100
    iget v2, p0, Lcom/android/support/Menu;->TEXT_COLOR_6:I

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1101
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 1102
    sget-object v2, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    const/4 v3, 0x1

    invoke-virtual {v1, v2, v3}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 1103
    const/4 v2, 0x3

    invoke-direct {p0, v2}, Lcom/android/support/Menu;->dp(I)I

    move-result v3

    invoke-direct {p0, v2}, Lcom/android/support/Menu;->dp(I)I

    move-result v2

    invoke-virtual {v1, v4, v3, v4, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 1104
    new-instance v2, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v2}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 1105
    .local v2, "gradientDrawable":Landroid/graphics/drawable/GradientDrawable;
    iget v3, p0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v2, v3}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1106
    const/16 v3, 0x64

    invoke-direct {p0, v3}, Lcom/android/support/Menu;->dp(I)I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {v2, v3}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1107
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1108
    if-nez p2, :cond_0

    .line 1109
    iput-object v1, p0, Lcom/android/support/Menu;->categorymaintext:Landroid/widget/TextView;

    .line 1111
    :cond_0
    invoke-virtual {p1, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1112
    return-void
.end method

.method private CheckBox(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
    .locals 2
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "switchedOn"    # Z

    .line 981
    new-instance v0, Landroid/widget/CheckBox;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/CheckBox;-><init>(Landroid/content/Context;)V

    .line 982
    .local v0, "checkBox":Landroid/widget/CheckBox;
    invoke-virtual {v0, p3}, Landroid/widget/CheckBox;->setText(Ljava/lang/CharSequence;)V

    .line 983
    iget v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v0, v1}, Landroid/widget/CheckBox;->setTextColor(I)V

    .line 984
    nop

    .line 985
    iget v1, p0, Lcom/android/support/Menu;->CheckBoxColor:I

    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/CheckBox;->setButtonTintList(Landroid/content/res/ColorStateList;)V

    .line 986
    invoke-static {p3, p2, p4}, Lcom/android/support/Preferences;->loadPrefBool(Ljava/lang/String;IZ)Z

    move-result v1

    invoke-virtual {v0, v1}, Landroid/widget/CheckBox;->setChecked(Z)V

    .line 987
    new-instance v1, Lcom/android/support/Menu$16;

    invoke-direct {v1, p0, v0, p3, p2}, Lcom/android/support/Menu$16;-><init>(Lcom/android/support/Menu;Landroid/widget/CheckBox;Ljava/lang/String;I)V

    invoke-virtual {v0, v1}, Landroid/widget/CheckBox;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 997
    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 998
    return-void
.end method

.method private Collapse(Landroid/widget/LinearLayout;Ljava/lang/String;)V
    .locals 8
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "text"    # Ljava/lang/String;

    .line 1045
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v1, -0x1

    invoke-direct {v0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 1046
    .local v0, "layoutParamsLL":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v1, 0x0

    const/4 v2, 0x5

    invoke-virtual {v0, v1, v2, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 1048
    new-instance v3, Landroid/widget/LinearLayout;

    iget-object v4, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v3, v4}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 1049
    .local v3, "collapse":Landroid/widget/LinearLayout;
    invoke-virtual {v3, v0}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 1050
    const/16 v4, 0x10

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 1051
    const/4 v5, 0x1

    invoke-virtual {v3, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 1053
    new-instance v6, Landroid/widget/LinearLayout;

    iget-object v7, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v6, v7}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 1054
    .local v6, "collapseSub":Landroid/widget/LinearLayout;
    invoke-virtual {v6, v4}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 1055
    invoke-virtual {v6, v1, v2, v1, v2}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 1056
    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 1057
    const-string v2, "#222222"

    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 1058
    const/16 v2, 0x8

    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 1059
    iput-object v6, p0, Lcom/android/support/Menu;->mCollapse:Landroid/widget/LinearLayout;

    .line 1061
    new-instance v2, Landroid/widget/TextView;

    iget-object v4, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v2, v4}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 1062
    .local v2, "textView":Landroid/widget/TextView;
    iget v4, p0, Lcom/android/support/Menu;->CategoryBG:I

    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setBackgroundColor(I)V

    .line 1063
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "\u25bc "

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v7, " \u25bc"

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1064
    const/16 v4, 0x11

    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 1065
    iget v4, p0, Lcom/android/support/Menu;->TEXT_COLOR_3:I

    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1066
    const/4 v4, 0x0

    invoke-virtual {v2, v4, v5}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 1067
    const/16 v4, 0x1e

    invoke-virtual {v2, v1, v4, v1, v4}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 1068
    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 1069
    .local v1, "gradientDrawable":Landroid/graphics/drawable/GradientDrawable;
    iget v4, p0, Lcom/android/support/Menu;->TEXT_COLOR_5:I

    invoke-virtual {v1, v4}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1070
    const/16 v4, 0xa

    invoke-direct {p0, v4}, Lcom/android/support/Menu;->dp(I)I

    move-result v4

    int-to-float v4, v4

    invoke-virtual {v1, v4}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1071
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1072
    new-instance v4, Lcom/android/support/Menu$18;

    invoke-direct {v4, p0, v6, v2, p2}, Lcom/android/support/Menu$18;-><init>(Lcom/android/support/Menu;Landroid/widget/LinearLayout;Landroid/widget/TextView;Ljava/lang/String;)V

    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 1089
    invoke-virtual {v3, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1090
    invoke-virtual {v3, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1091
    invoke-virtual {p1, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1092
    return-void
.end method

.method private InputNum(Landroid/widget/LinearLayout;ILjava/lang/String;I)V
    .locals 10
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "maxValue"    # I

    .line 822
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 823
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 824
    .local v1, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v2, 0x7

    const/4 v3, 0x5

    invoke-virtual {v1, v2, v3, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 826
    new-instance v2, Landroid/widget/Button;

    iget-object v3, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v2, v3}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    move-object v7, v2

    .line 827
    .local v7, "button":Landroid/widget/Button;
    invoke-static {p3, p2}, Lcom/android/support/Preferences;->loadPrefInt(Ljava/lang/String;I)I

    move-result v2

    .line 828
    .local v2, "num":I
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ": <font color=\'"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v4, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "\'>"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    if-nez v2, :cond_0

    const/4 v4, 0x1

    goto :goto_0

    :cond_0
    move v4, v2

    :goto_0
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "</font>"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {p0, v3}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v3

    invoke-virtual {v7, v3}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 829
    const/4 v3, 0x0

    invoke-virtual {v7, v3}, Landroid/widget/Button;->setAllCaps(Z)V

    .line 830
    invoke-virtual {v7, v1}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 831
    iget v3, p0, Lcom/android/support/Menu;->BTN_COLOR:I

    invoke-virtual {v7, v3}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 832
    iget v3, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v7, v3}, Landroid/widget/Button;->setTextColor(I)V

    .line 833
    new-instance v4, Lcom/android/support/Menu$14;

    move-object v5, p0

    move v9, p2

    move-object v8, p3

    move v6, p4

    .end local p2    # "featNum":I
    .end local p3    # "featName":Ljava/lang/String;
    .end local p4    # "maxValue":I
    .local v6, "maxValue":I
    .local v8, "featName":Ljava/lang/String;
    .local v9, "featNum":I
    invoke-direct/range {v4 .. v9}, Lcom/android/support/Menu$14;-><init>(Lcom/android/support/Menu;ILandroid/widget/Button;Ljava/lang/String;I)V

    invoke-virtual {v7, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 904
    invoke-virtual {v0, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 905
    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 906
    return-void
.end method

.method private InputText(Landroid/widget/LinearLayout;ILjava/lang/String;)V
    .locals 6
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;

    .line 909
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 910
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 911
    .local v1, "layoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v2, 0x7

    const/4 v3, 0x5

    invoke-virtual {v1, v2, v3, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 913
    new-instance v2, Landroid/widget/Button;

    iget-object v3, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v2, v3}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 915
    .local v2, "button":Landroid/widget/Button;
    invoke-static {p3, p2}, Lcom/android/support/Preferences;->loadPrefString(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v3

    .line 916
    .local v3, "string":Ljava/lang/String;
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, ": <font color=\'"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    iget-object v5, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "\'>"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "</font>"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-direct {p0, v4}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v4

    invoke-virtual {v2, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 918
    const/4 v4, 0x0

    invoke-virtual {v2, v4}, Landroid/widget/Button;->setAllCaps(Z)V

    .line 919
    invoke-virtual {v2, v1}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 920
    iget v4, p0, Lcom/android/support/Menu;->BTN_COLOR:I

    invoke-virtual {v2, v4}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 921
    iget v4, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v2, v4}, Landroid/widget/Button;->setTextColor(I)V

    .line 922
    new-instance v4, Lcom/android/support/Menu$15;

    invoke-direct {v4, p0, v2, p3, p2}, Lcom/android/support/Menu$15;-><init>(Lcom/android/support/Menu;Landroid/widget/Button;Ljava/lang/String;I)V

    invoke-virtual {v2, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 976
    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 977
    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 978
    return-void
.end method

.method private RadioButton(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
    .locals 14
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "list"    # Ljava/lang/String;

    .line 1007
    move-object/from16 v0, p3

    new-instance v1, Ljava/util/LinkedList;

    const-string v2, ","

    move-object/from16 v3, p4

    invoke-virtual {v3, v2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    .line 1009
    .local v1, "lists":Ljava/util/List;, "Ljava/util/List<Ljava/lang/String;>;"
    new-instance v2, Landroid/widget/TextView;

    iget-object v4, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v2, v4}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v7, v2

    .line 1010
    .local v7, "textView":Landroid/widget/TextView;
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v4, ":"

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v7, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1011
    iget v2, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v7, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1013
    new-instance v11, Landroid/widget/RadioGroup;

    iget-object v2, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v11, v2}, Landroid/widget/RadioGroup;-><init>(Landroid/content/Context;)V

    .line 1014
    .local v11, "radioGroup":Landroid/widget/RadioGroup;
    const/16 v2, 0xa

    const/4 v4, 0x5

    invoke-virtual {v11, v2, v4, v2, v4}, Landroid/widget/RadioGroup;->setPadding(IIII)V

    .line 1015
    const/4 v2, 0x1

    invoke-virtual {v11, v2}, Landroid/widget/RadioGroup;->setOrientation(I)V

    .line 1016
    invoke-virtual {v11, v7}, Landroid/widget/RadioGroup;->addView(Landroid/view/View;)V

    .line 1018
    const/4 v4, 0x0

    .local v4, "i":I
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v5

    if-ge v4, v5, :cond_0

    .line 1019
    new-instance v12, Landroid/widget/RadioButton;

    iget-object v5, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v12, v5}, Landroid/widget/RadioButton;-><init>(Landroid/content/Context;)V

    .line 1020
    .local v12, "Radioo":Landroid/widget/RadioButton;
    move-object/from16 v8, p3

    .local v8, "finalfeatName":Ljava/lang/String;
    invoke-interface {v1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    move-object v9, v5

    check-cast v9, Ljava/lang/String;

    .line 1021
    .local v9, "radioName":Ljava/lang/String;
    new-instance v5, Lcom/android/support/Menu$17;

    move-object v6, p0

    move/from16 v10, p2

    invoke-direct/range {v5 .. v12}, Lcom/android/support/Menu$17;-><init>(Lcom/android/support/Menu;Landroid/widget/TextView;Ljava/lang/String;Ljava/lang/String;ILandroid/widget/RadioGroup;Landroid/widget/RadioButton;)V

    .line 1027
    .local v5, "first_radio_listener":Landroid/view/View$OnClickListener;
    sget-object v10, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-interface {v1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Ljava/lang/String;

    invoke-virtual {v10, v13}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1028
    invoke-interface {v1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/lang/CharSequence;

    invoke-virtual {v12, v10}, Landroid/widget/RadioButton;->setText(Ljava/lang/CharSequence;)V

    .line 1029
    const v10, -0x333334

    invoke-virtual {v12, v10}, Landroid/widget/RadioButton;->setTextColor(I)V

    .line 1030
    nop

    .line 1031
    iget v10, p0, Lcom/android/support/Menu;->RadioColor:I

    invoke-static {v10}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v10

    invoke-virtual {v12, v10}, Landroid/widget/RadioButton;->setButtonTintList(Landroid/content/res/ColorStateList;)V

    .line 1032
    invoke-virtual {v12, v5}, Landroid/widget/RadioButton;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 1033
    invoke-virtual {v11, v12}, Landroid/widget/RadioGroup;->addView(Landroid/view/View;)V

    .line 1018
    .end local v5    # "first_radio_listener":Landroid/view/View$OnClickListener;
    .end local v8    # "finalfeatName":Ljava/lang/String;
    .end local v9    # "radioName":Ljava/lang/String;
    .end local v12    # "Radioo":Landroid/widget/RadioButton;
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 1036
    .end local v4    # "i":I
    :cond_0
    move/from16 v10, p2

    invoke-static {v0, v10}, Lcom/android/support/Preferences;->loadPrefInt(Ljava/lang/String;I)I

    move-result v4

    .line 1037
    .local v4, "index":I
    if-lez v4, :cond_1

    .line 1038
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v8, ": <font color=\'"

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v8, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v8, "\'>"

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    add-int/lit8 v8, v4, -0x1

    invoke-interface {v1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/lang/String;

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {p0, v5}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v5

    invoke-virtual {v7, v5}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1039
    invoke-virtual {v11, v4}, Landroid/widget/RadioGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v5

    check-cast v5, Landroid/widget/RadioButton;

    invoke-virtual {v5, v2}, Landroid/widget/RadioButton;->setChecked(Z)V

    .line 1041
    :cond_1
    invoke-virtual {p1, v11}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1042
    return-void
.end method

.method private SeekBar(Landroid/widget/LinearLayout;ILjava/lang/String;II)V
    .locals 11
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "min"    # I
    .param p5, "max"    # I

    .line 657
    invoke-static {p3, p2}, Lcom/android/support/Preferences;->loadPrefInt(Ljava/lang/String;I)I

    move-result v6

    .line 658
    .local v6, "loadedProg":I
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object v7, v0

    .line 659
    .local v7, "linearLayout":Landroid/widget/LinearLayout;
    const/4 v0, 0x5

    const/4 v1, 0x0

    const/16 v2, 0xa

    invoke-virtual {v7, v2, v0, v1, v0}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 660
    const/4 v0, 0x1

    invoke-virtual {v7, v0}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 661
    const/16 v0, 0x11

    invoke-virtual {v7, v0}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 663
    new-instance v5, Landroid/widget/TextView;

    iget-object v0, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v5, v0}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 664
    .local v5, "textView":Landroid/widget/TextView;
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ": <font color=\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    if-nez v6, :cond_0

    move v1, p4

    goto :goto_0

    :cond_0
    move v1, v6

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v0

    invoke-virtual {v5, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 665
    iget v0, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v5, v0}, Landroid/widget/TextView;->setTextColor(I)V

    .line 667
    new-instance v0, Landroid/widget/SeekBar;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object v8, v0

    .line 668
    .local v8, "seekBar":Landroid/widget/SeekBar;
    const/16 v0, 0x19

    const/16 v1, 0x23

    invoke-virtual {v8, v0, v2, v1, v2}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 669
    move/from16 v9, p5

    invoke-virtual {v8, v9}, Landroid/widget/SeekBar;->setMax(I)V

    .line 670
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_1

    .line 671
    invoke-virtual {v8, p4}, Landroid/widget/SeekBar;->setMin(I)V

    .line 672
    :cond_1
    if-nez v6, :cond_2

    move v0, p4

    goto :goto_1

    :cond_2
    move v0, v6

    :goto_1
    invoke-virtual {v8, v0}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 673
    invoke-virtual {v8}, Landroid/widget/SeekBar;->getThumb()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    new-instance v1, Landroid/graphics/PorterDuffColorFilter;

    iget v4, p0, Lcom/android/support/Menu;->SeekBarColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_ATOP:Landroid/graphics/PorterDuff$Mode;

    invoke-direct {v1, v4, v10}, Landroid/graphics/PorterDuffColorFilter;-><init>(ILandroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/Drawable;->setColorFilter(Landroid/graphics/ColorFilter;)V

    .line 674
    invoke-virtual {v8}, Landroid/widget/SeekBar;->getProgressDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    new-instance v1, Landroid/graphics/PorterDuffColorFilter;

    iget v4, p0, Lcom/android/support/Menu;->SeekBarProgressColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_ATOP:Landroid/graphics/PorterDuff$Mode;

    invoke-direct {v1, v4, v10}, Landroid/graphics/PorterDuffColorFilter;-><init>(ILandroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/Drawable;->setColorFilter(Landroid/graphics/ColorFilter;)V

    .line 675
    new-instance v0, Lcom/android/support/Menu$9;

    move-object v1, p0

    move v4, p2

    move-object v3, p3

    move v2, p4

    invoke-direct/range {v0 .. v5}, Lcom/android/support/Menu$9;-><init>(Lcom/android/support/Menu;ILjava/lang/String;ILandroid/widget/TextView;)V

    invoke-virtual {v8, v0}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 689
    invoke-virtual {v7, v5}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 690
    invoke-virtual {v7, v8}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 692
    invoke-virtual {p1, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 693
    return-void
.end method

.method private Spinner(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
    .locals 8
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "list"    # Ljava/lang/String;

    .line 785
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "spinner "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Mod_Menu"

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 786
    new-instance v0, Ljava/util/LinkedList;

    const-string v1, ","

    invoke-virtual {p4, v1}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    .line 790
    .local v0, "lists":Ljava/util/List;, "Ljava/util/List<Ljava/lang/String;>;"
    new-instance v1, Landroid/widget/LinearLayout;

    iget-object v2, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 791
    .local v1, "linearLayout2":Landroid/widget/LinearLayout;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x1

    const/4 v4, -0x2

    invoke-direct {v2, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 792
    .local v2, "layoutParams2":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v3, 0x7

    const/4 v4, 0x2

    invoke-virtual {v2, v3, v4, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 793
    const/4 v3, 0x1

    invoke-virtual {v1, v3}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 794
    iget v4, p0, Lcom/android/support/Menu;->BTN_COLOR:I

    invoke-virtual {v1, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 795
    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 797
    new-instance v4, Landroid/widget/Spinner;

    iget-object v5, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v4, v5, v3}, Landroid/widget/Spinner;-><init>(Landroid/content/Context;I)V

    .line 798
    .local v4, "spinner":Landroid/widget/Spinner;
    invoke-virtual {v4, v2}, Landroid/widget/Spinner;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 799
    invoke-virtual {v4}, Landroid/widget/Spinner;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    new-instance v6, Landroid/graphics/PorterDuffColorFilter;

    sget-object v7, Landroid/graphics/PorterDuff$Mode;->SRC_ATOP:Landroid/graphics/PorterDuff$Mode;

    invoke-direct {v6, v3, v7}, Landroid/graphics/PorterDuffColorFilter;-><init>(ILandroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v5, v6}, Landroid/graphics/drawable/Drawable;->setColorFilter(Landroid/graphics/ColorFilter;)V

    .line 801
    new-instance v3, Landroid/widget/ArrayAdapter;

    iget-object v5, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    const v6, 0x1090009

    invoke-direct {v3, v5, v6, v0}, Landroid/widget/ArrayAdapter;-><init>(Landroid/content/Context;ILjava/util/List;)V

    .line 802
    .local v3, "aa":Landroid/widget/ArrayAdapter;, "Landroid/widget/ArrayAdapter<Ljava/lang/String;>;"
    invoke-virtual {v3, v6}, Landroid/widget/ArrayAdapter;->setDropDownViewResource(I)V

    .line 804
    invoke-virtual {v4, v3}, Landroid/widget/Spinner;->setAdapter(Landroid/widget/SpinnerAdapter;)V

    .line 805
    invoke-static {p3, p2}, Lcom/android/support/Preferences;->loadPrefInt(Ljava/lang/String;I)I

    move-result v5

    invoke-virtual {v4, v5}, Landroid/widget/Spinner;->setSelection(I)V

    .line 806
    new-instance v5, Lcom/android/support/Menu$13;

    invoke-direct {v5, p0, v4, p2}, Lcom/android/support/Menu$13;-><init>(Lcom/android/support/Menu;Landroid/widget/Spinner;I)V

    invoke-virtual {v4, v5}, Landroid/widget/Spinner;->setOnItemSelectedListener(Landroid/widget/AdapterView$OnItemSelectedListener;)V

    .line 817
    invoke-virtual {v1, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 818
    invoke-virtual {p1, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 819
    return-void
.end method

.method private Switch(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
    .locals 10
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "featNum"    # I
    .param p3, "featName"    # Ljava/lang/String;
    .param p4, "swiOn"    # Z

    .line 580
    new-instance v0, Landroid/widget/Switch;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/Switch;-><init>(Landroid/content/Context;)V

    move-object v7, v0

    .line 581
    .local v7, "switchR":Landroid/widget/Switch;
    new-instance v0, Landroid/content/res/ColorStateList;

    const/4 v1, 0x0

    new-array v2, v1, [I

    const/4 v3, 0x3

    new-array v3, v3, [[I

    const v4, -0x101009e

    filled-new-array {v4}, [I

    move-result-object v4

    aput-object v4, v3, v1

    const v4, 0x10100a0

    filled-new-array {v4}, [I

    move-result-object v4

    const/4 v5, 0x1

    aput-object v4, v3, v5

    const/4 v4, 0x2

    aput-object v2, v3, v4

    iget v2, p0, Lcom/android/support/Menu;->ToggleON:I

    iget v4, p0, Lcom/android/support/Menu;->ToggleOFF:I

    const v6, -0xffff01

    filled-new-array {v6, v2, v4}, [I

    move-result-object v2

    invoke-direct {v0, v3, v2}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    .line 595
    .local v0, "buttonStates":Landroid/content/res/ColorStateList;
    nop

    .line 596
    invoke-virtual {v7}, Landroid/widget/Switch;->getThumbDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v2

    invoke-virtual {v2, v0}, Landroid/graphics/drawable/Drawable;->setTintList(Landroid/content/res/ColorStateList;)V

    .line 597
    invoke-virtual {v7}, Landroid/widget/Switch;->getTrackDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v2

    invoke-virtual {v2, v0}, Landroid/graphics/drawable/Drawable;->setTintList(Landroid/content/res/ColorStateList;)V

    .line 600
    invoke-virtual {v7, p3}, Landroid/widget/Switch;->setText(Ljava/lang/CharSequence;)V

    .line 601
    iget v2, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v7, v2}, Landroid/widget/Switch;->setTextColor(I)V

    .line 602
    const/4 v2, 0x5

    invoke-virtual {v7, v1, v2, v1, v2}, Landroid/widget/Switch;->setPadding(IIII)V

    .line 603
    invoke-virtual {v7, v1}, Landroid/widget/Switch;->setBackgroundColor(I)V

    .line 604
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x1

    const/4 v4, -0x2

    invoke-direct {v2, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v7, v2}, Landroid/widget/Switch;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 605
    new-instance v2, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v2}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v8, v2

    .line 606
    .local v8, "g":Landroid/graphics/drawable/GradientDrawable;
    const/16 v2, 0x32

    invoke-virtual {v8, v2, v2}, Landroid/graphics/drawable/GradientDrawable;->setSize(II)V

    .line 607
    invoke-virtual {v8, v5}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    .line 608
    const/16 v2, 0xa

    invoke-virtual {v8, v2, v1}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 609
    const-string v1, "#FFFFFFFF"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v8, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 610
    new-instance v6, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v6}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 611
    .local v6, "gd":Landroid/graphics/drawable/GradientDrawable;
    const/16 v1, 0x28

    const/4 v2, 0x4

    invoke-virtual {v6, v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setSize(II)V

    .line 612
    const/high16 v1, 0x42480000    # 50.0f

    invoke-virtual {v6, v1}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 613
    const-string v1, "#434343"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    invoke-virtual {v6, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 614
    invoke-virtual {v7, v8}, Landroid/widget/Switch;->setThumbDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 615
    invoke-virtual {v7, v6}, Landroid/widget/Switch;->setTrackDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 617
    invoke-static {p3, p2, p4}, Lcom/android/support/Preferences;->loadPrefBool(Ljava/lang/String;IZ)Z

    move-result v9

    .line 619
    .local v9, "isOn":Z
    if-eqz v9, :cond_0

    .line 620
    iget v1, p0, Lcom/android/support/Menu;->CategoryBG:I

    invoke-virtual {v6, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    goto :goto_0

    .line 622
    :cond_0
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v6, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 626
    :goto_0
    invoke-static {p3, p2, p4}, Lcom/android/support/Preferences;->loadPrefBool(Ljava/lang/String;IZ)Z

    move-result v1

    invoke-virtual {v7, v1}, Landroid/widget/Switch;->setChecked(Z)V

    .line 627
    new-instance v2, Lcom/android/support/Menu$8;

    move-object v3, p0

    move v5, p2

    move-object v4, p3

    .end local p2    # "featNum":I
    .end local p3    # "featName":Ljava/lang/String;
    .local v4, "featName":Ljava/lang/String;
    .local v5, "featNum":I
    invoke-direct/range {v2 .. v7}, Lcom/android/support/Menu$8;-><init>(Lcom/android/support/Menu;Ljava/lang/String;ILandroid/graphics/drawable/GradientDrawable;Landroid/widget/Switch;)V

    invoke-virtual {v7, v2}, Landroid/widget/Switch;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 652
    invoke-virtual {p1, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 653
    return-void
.end method

.method private TextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
    .locals 3
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "text"    # Ljava/lang/String;

    .line 1115
    new-instance v0, Landroid/widget/TextView;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 1116
    .local v0, "textView":Landroid/widget/TextView;
    invoke-direct {p0, p2}, Lcom/android/support/Menu;->fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1117
    iget v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1118
    const/16 v1, 0xa

    const/4 v2, 0x5

    invoke-virtual {v0, v1, v2, v1, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 1119
    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1120
    return-void
.end method

.method private WebTextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
    .locals 3
    .param p1, "linLayout"    # Landroid/widget/LinearLayout;
    .param p2, "text"    # Ljava/lang/String;

    .line 1123
    new-instance v0, Landroid/webkit/WebView;

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/webkit/WebView;-><init>(Landroid/content/Context;)V

    .line 1124
    .local v0, "wView":Landroid/webkit/WebView;
    const-string v1, "text/html"

    const-string v2, "utf-8"

    invoke-virtual {v0, p2, v1, v2}, Landroid/webkit/WebView;->loadData(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 1125
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/webkit/WebView;->setBackgroundColor(I)V

    .line 1126
    const/4 v2, 0x5

    invoke-virtual {v0, v1, v2, v1, v2}, Landroid/webkit/WebView;->setPadding(IIII)V

    .line 1127
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    move-result-object v1

    const/4 v2, 0x2

    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setCacheMode(I)V

    .line 1128
    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1129
    return-void
.end method

.method private convertDipToPixels(I)I
    .locals 2
    .param p1, "i"    # I

    .line 1137
    int-to-float v0, p1

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v1

    iget v1, v1, Landroid/util/DisplayMetrics;->density:F

    mul-float v0, v0, v1

    const/high16 v1, 0x3f000000    # 0.5f

    add-float/2addr v0, v1

    float-to-int v0, v0

    return v0
.end method

.method private dp(I)I
    .locals 3
    .param p1, "i"    # I

    .line 1141
    int-to-float v0, p1

    iget-object v1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v1

    const/4 v2, 0x1

    invoke-static {v2, v0, v1}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v0

    float-to-int v0, v0

    return v0
.end method

.method private featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V
    .locals 17
    .param p1, "listFT"    # [Ljava/lang/String;
    .param p2, "linearLayout"    # Landroid/widget/LinearLayout;

    .line 493
    move-object/from16 v0, p0

    move-object/from16 v6, p1

    const/4 v1, 0x0

    .line 494
    .local v1, "subFeat":I
    move-object/from16 v7, p2

    .line 496
    .local v7, "llBak":Landroid/widget/LinearLayout;
    const/4 v2, 0x0

    move v8, v2

    move v2, v1

    move-object/from16 v1, p2

    .end local p2    # "linearLayout":Landroid/widget/LinearLayout;
    .local v1, "linearLayout":Landroid/widget/LinearLayout;
    .local v2, "subFeat":I
    .local v8, "i":I
    :goto_0
    array-length v3, v6

    if-ge v8, v3, :cond_7

    .line 497
    const/4 v3, 0x0

    .line 499
    .local v3, "switchedOn":Z
    aget-object v4, v6, v8

    .line 500
    .local v4, "feature":Ljava/lang/String;
    const-string v5, "_True"

    invoke-virtual {v4, v5}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v9

    const-string v10, ""

    if-eqz v9, :cond_0

    .line 501
    const/4 v3, 0x1

    .line 502
    invoke-virtual {v4, v5, v10}, Ljava/lang/String;->replaceFirst(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    move v9, v3

    goto :goto_1

    .line 500
    :cond_0
    move v9, v3

    .line 505
    .end local v3    # "switchedOn":Z
    .local v9, "switchedOn":Z
    :goto_1
    move-object v1, v7

    .line 506
    const-string v3, "CollapseAdd_"

    invoke-virtual {v4, v3}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_1

    .line 508
    iget-object v1, v0, Lcom/android/support/Menu;->mCollapse:Landroid/widget/LinearLayout;

    .line 509
    invoke-virtual {v4, v3, v10}, Ljava/lang/String;->replaceFirst(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    .line 511
    :cond_1
    const-string v3, "_"

    invoke-virtual {v4, v3}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v11

    .line 514
    .local v11, "str":[Ljava/lang/String;
    const/4 v5, 0x0

    aget-object v12, v11, v5

    invoke-static {v12}, Landroid/text/TextUtils;->isDigitsOnly(Ljava/lang/CharSequence;)Z

    move-result v12

    if-nez v12, :cond_3

    aget-object v12, v11, v5

    const-string v13, "-[0-9]*"

    invoke-virtual {v12, v13}, Ljava/lang/String;->matches(Ljava/lang/String;)Z

    move-result v12

    if-eqz v12, :cond_2

    goto :goto_2

    .line 520
    :cond_2
    sub-int v10, v8, v2

    move v12, v10

    move v10, v2

    move v2, v12

    move-object v12, v4

    .local v10, "featNum":I
    goto :goto_3

    .line 515
    .end local v10    # "featNum":I
    :cond_3
    :goto_2
    aget-object v12, v11, v5

    invoke-static {v12}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v12

    .line 516
    .local v12, "featNum":I
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    aget-object v14, v11, v5

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    invoke-virtual {v13, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v4, v13, v10}, Ljava/lang/String;->replaceFirst(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    .line 517
    add-int/lit8 v2, v2, 0x1

    move v10, v2

    move v2, v12

    move-object v12, v4

    .line 522
    .end local v4    # "feature":Ljava/lang/String;
    .local v2, "featNum":I
    .local v10, "subFeat":I
    .local v12, "feature":Ljava/lang/String;
    :goto_3
    invoke-virtual {v12, v3}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v13

    .line 523
    .local v13, "strSplit":[Ljava/lang/String;
    aget-object v3, v13, v5

    invoke-virtual {v3}, Ljava/lang/String;->hashCode()I

    move-result v4

    const/4 v14, 0x3

    const/4 v15, 0x2

    const/16 v16, 0x1

    sparse-switch v4, :sswitch_data_0

    :cond_4
    goto/16 :goto_4

    :sswitch_0
    const-string v4, "Button"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x2

    goto/16 :goto_5

    :sswitch_1
    const-string v4, "CheckBox"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x7

    goto/16 :goto_5

    :sswitch_2
    const-string v4, "RadioButton"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0x8

    goto/16 :goto_5

    :sswitch_3
    const-string v4, "RichWebView"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0xd

    goto/16 :goto_5

    :sswitch_4
    const-string v4, "Category"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0xb

    goto :goto_5

    :sswitch_5
    const-string v4, "RichTextView"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0xc

    goto :goto_5

    :sswitch_6
    const-string v4, "ButtonOnOff"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x3

    goto :goto_5

    :sswitch_7
    const-string v4, "Spinner"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x4

    goto :goto_5

    :sswitch_8
    const-string v4, "Collapse"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0x9

    goto :goto_5

    :sswitch_9
    const-string v4, "InputText"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x5

    goto :goto_5

    :sswitch_a
    const-string v4, "SeekBar"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x1

    goto :goto_5

    :sswitch_b
    const-string v4, "InputValue"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x6

    goto :goto_5

    :sswitch_c
    const-string v4, "Toggle"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x0

    goto :goto_5

    :sswitch_d
    const-string v4, "ButtonLink"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    const/16 v3, 0xa

    goto :goto_5

    :goto_4
    const/4 v3, -0x1

    :goto_5
    packed-switch v3, :pswitch_data_0

    goto/16 :goto_6

    .line 572
    :pswitch_0
    add-int/lit8 v10, v10, 0x1

    .line 573
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v3}, Lcom/android/support/Menu;->WebTextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V

    goto/16 :goto_6

    .line 568
    :pswitch_1
    add-int/lit8 v10, v10, 0x1

    .line 569
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v3}, Lcom/android/support/Menu;->TextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V

    .line 570
    goto/16 :goto_6

    .line 564
    :pswitch_2
    add-int/lit8 v10, v10, 0x1

    .line 565
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v9, v3}, Lcom/android/support/Menu;->Category(Landroid/widget/LinearLayout;ZLjava/lang/String;)V

    .line 566
    goto/16 :goto_6

    .line 560
    :pswitch_3
    add-int/lit8 v10, v10, 0x1

    .line 561
    aget-object v3, v13, v16

    aget-object v4, v13, v15

    invoke-direct {v0, v3, v4}, Lcom/android/support/Menu;->ButtonLink(Ljava/lang/String;Ljava/lang/String;)Landroid/view/View;

    move-result-object v3

    invoke-virtual {v1, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 562
    goto :goto_6

    .line 556
    :pswitch_4
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v3}, Lcom/android/support/Menu;->Collapse(Landroid/widget/LinearLayout;Ljava/lang/String;)V

    .line 557
    add-int/lit8 v10, v10, 0x1

    .line 558
    goto :goto_6

    .line 553
    :pswitch_5
    aget-object v3, v13, v16

    aget-object v4, v13, v15

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/support/Menu;->RadioButton(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V

    .line 554
    goto :goto_6

    .line 550
    :pswitch_6
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3, v9}, Lcom/android/support/Menu;->CheckBox(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V

    .line 551
    goto :goto_6

    .line 544
    :pswitch_7
    array-length v3, v13

    if-ne v3, v14, :cond_5

    .line 545
    aget-object v3, v13, v15

    aget-object v4, v13, v16

    invoke-static {v4}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/support/Menu;->InputNum(Landroid/widget/LinearLayout;ILjava/lang/String;I)V

    .line 546
    :cond_5
    array-length v3, v13

    if-ne v3, v15, :cond_6

    .line 547
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3, v5}, Lcom/android/support/Menu;->InputNum(Landroid/widget/LinearLayout;ILjava/lang/String;I)V

    goto :goto_6

    .line 541
    :pswitch_8
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3}, Lcom/android/support/Menu;->InputText(Landroid/widget/LinearLayout;ILjava/lang/String;)V

    .line 542
    goto :goto_6

    .line 537
    :pswitch_9
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v3}, Lcom/android/support/Menu;->TextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V

    .line 538
    aget-object v3, v13, v16

    aget-object v4, v13, v15

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/support/Menu;->Spinner(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V

    .line 539
    goto :goto_6

    .line 534
    :pswitch_a
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3, v9}, Lcom/android/support/Menu;->ButtonOnOff(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V

    .line 535
    goto :goto_6

    .line 531
    :pswitch_b
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3}, Lcom/android/support/Menu;->Button(Landroid/widget/LinearLayout;ILjava/lang/String;)V

    .line 532
    goto :goto_6

    .line 528
    :pswitch_c
    aget-object v3, v13, v16

    aget-object v4, v13, v15

    invoke-static {v4}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v4

    aget-object v5, v13, v14

    invoke-static {v5}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v5

    invoke-direct/range {v0 .. v5}, Lcom/android/support/Menu;->SeekBar(Landroid/widget/LinearLayout;ILjava/lang/String;II)V

    .line 529
    goto :goto_6

    .line 525
    :pswitch_d
    aget-object v3, v13, v16

    invoke-direct {v0, v1, v2, v3, v9}, Lcom/android/support/Menu;->Switch(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V

    .line 526
    nop

    .line 496
    .end local v9    # "switchedOn":Z
    .end local v11    # "str":[Ljava/lang/String;
    .end local v12    # "feature":Ljava/lang/String;
    .end local v13    # "strSplit":[Ljava/lang/String;
    :cond_6
    :goto_6
    add-int/lit8 v8, v8, 0x1

    move v2, v10

    goto/16 :goto_0

    .line 577
    .end local v8    # "i":I
    .end local v10    # "subFeat":I
    .local v2, "subFeat":I
    :cond_7
    return-void

    :sswitch_data_0
    .sparse-switch
        -0x73d2c194 -> :sswitch_d
        -0x6a5c588c -> :sswitch_c
        -0x370e8c19 -> :sswitch_b
        -0x274065a5 -> :sswitch_a
        -0x22cfc409 -> :sswitch_9
        -0x21d27833 -> :sswitch_8
        -0x1440b607 -> :sswitch_7
        -0x6595922 -> :sswitch_6
        0x4f11a8e -> :sswitch_5
        0x6dd211e -> :sswitch_4
        0x1977177d -> :sswitch_3
        0x2e46a6ed -> :sswitch_2
        0x5f7507c3 -> :sswitch_1
        0x77471352 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method private fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;
    .locals 2
    .param p1, "html"    # Ljava/lang/String;

    .line 154
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 155
    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroid/text/Html;->fromHtml(Ljava/lang/String;I)Landroid/text/Spanned;

    move-result-object v0

    return-object v0

    .line 158
    :cond_0
    invoke-static {p1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v0

    .line 159
    .local v0, "result":Ljava/lang/CharSequence;
    return-object v0
.end method

.method private initializeMenu(Landroid/content/Context;)V
    .locals 22
    .param p1, "context"    # Landroid/content/Context;

    .line 171
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    iput-object v1, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    .line 172
    sput-object v1, Lcom/android/support/Preferences;->context:Landroid/content/Context;

    .line 173
    new-instance v2, Landroid/widget/FrameLayout;

    invoke-direct {v2, v1}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    .line 174
    iget-object v2, v0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/FrameLayout;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 175
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    .line 176
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    .line 177
    iget-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    const/4 v3, 0x0

    invoke-virtual {v2, v3}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 178
    iget-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    iget v4, v0, Lcom/android/support/Menu;->ICON_ALPHA:F

    invoke-virtual {v2, v4}, Landroid/widget/RelativeLayout;->setAlpha(F)V

    .line 181
    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    .line 182
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    const/16 v4, 0x8

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 183
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    iget v4, v0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 184
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    const/4 v4, 0x1

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 185
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v4, v4, v4, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 186
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    iget v6, v0, Lcom/android/support/Menu;->MENU_WIDTH:I

    invoke-direct {v0, v6}, Lcom/android/support/Menu;->dp(I)I

    move-result v6

    const/4 v7, -0x2

    invoke-direct {v5, v6, v7}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v5}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 187
    new-instance v2, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v2}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 188
    .local v2, "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    iget v5, v0, Lcom/android/support/Menu;->MENU_CORNER:F

    invoke-virtual {v2, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 189
    iget v5, v0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    invoke-virtual {v2, v5}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 190
    const-string v5, "#32cb00"

    invoke-static {v5}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    const/4 v6, 0x2

    invoke-virtual {v2, v6, v5}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 191
    iget-object v5, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v5, v2}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 194
    new-instance v5, Landroid/widget/ImageView;

    invoke-direct {v5, v1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    iput-object v5, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    .line 195
    iget v5, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    int-to-float v5, v5

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v6

    invoke-virtual {v6}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v6

    invoke-static {v4, v5, v6}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    float-to-int v5, v5

    .line 196
    .local v5, "applyDimension":I
    new-instance v6, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v6, v5, v5}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 197
    .local v6, "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v8, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v8, v6}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 199
    iget-object v8, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    sget-object v9, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v8, v9}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 200
    invoke-virtual {v0}, Lcom/android/support/Menu;->Icon()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8, v3}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v8

    .line 201
    .local v8, "decode":[B
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    array-length v10, v8

    invoke-static {v8, v3, v10}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v10

    invoke-virtual {v9, v10}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 202
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v9}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v9

    check-cast v9, Landroid/view/ViewGroup$MarginLayoutParams;

    const/16 v10, 0xa

    invoke-direct {v0, v10}, Lcom/android/support/Menu;->convertDipToPixels(I)I

    move-result v11

    iput v11, v9, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 204
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v11

    invoke-virtual {v9, v11}, Landroid/widget/ImageView;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 205
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    new-instance v11, Lcom/android/support/Menu$1;

    invoke-direct {v11, v0}, Lcom/android/support/Menu$1;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v9, v11}, Landroid/widget/ImageView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 213
    new-instance v9, Landroid/webkit/WebView;

    invoke-direct {v9, v1}, Landroid/webkit/WebView;-><init>(Landroid/content/Context;)V

    .line 214
    .local v9, "wView":Landroid/webkit/WebView;
    iget v11, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    int-to-float v11, v11

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v12

    invoke-virtual {v12}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v12

    invoke-static {v4, v11, v12}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v11

    float-to-int v11, v11

    .line 215
    .local v11, "applyDimension2":I
    new-instance v12, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v12, v11, v11}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 216
    .local v12, "wViewParams":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-virtual {v9, v12}, Landroid/webkit/WebView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 217
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "<html><head></head><body style=\"margin: 0; padding: 0\"><img src=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    .line 220
    invoke-virtual {v0}, Lcom/android/support/Menu;->IconWebViewData()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" width=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    iget v14, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" height=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    iget v14, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" ></body></html>"

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 217
    const-string v14, "text/html"

    const-string v15, "utf-8"

    invoke-virtual {v9, v13, v14, v15}, Landroid/webkit/WebView;->loadData(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 223
    invoke-virtual {v9, v3}, Landroid/webkit/WebView;->setBackgroundColor(I)V

    .line 224
    iget v13, v0, Lcom/android/support/Menu;->ICON_ALPHA:F

    invoke-virtual {v9, v13}, Landroid/webkit/WebView;->setAlpha(F)V

    .line 225
    invoke-virtual {v9}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    move-result-object v13

    const/4 v14, -0x1

    invoke-virtual {v13, v14}, Landroid/webkit/WebSettings;->setCacheMode(I)V

    .line 226
    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v13

    invoke-virtual {v9, v13}, Landroid/webkit/WebView;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 229
    new-instance v13, Landroid/widget/TextView;

    invoke-direct {v13, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 230
    .local v13, "settings":Landroid/widget/TextView;
    const-string v15, "\u2699"

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 231
    iget v15, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTextColor(I)V

    .line 232
    sget-object v15, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 233
    const/high16 v15, 0x41a00000    # 20.0f

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTextSize(F)V

    .line 234
    new-instance v15, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v15, v7, v7}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 235
    .local v15, "rlsettings":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v3, 0xb

    invoke-virtual {v15, v3}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 236
    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 237
    new-instance v3, Lcom/android/support/Menu$2;

    invoke-direct {v3, v0}, Lcom/android/support/Menu$2;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v13, v3}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 258
    new-instance v3, Landroid/widget/LinearLayout;

    invoke-direct {v3, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v3, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    .line 259
    iget-object v3, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 260
    invoke-virtual {v0}, Lcom/android/support/Menu;->SettingsList()[Ljava/lang/String;

    move-result-object v3

    iget-object v4, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    invoke-direct {v0, v3, v4}, Lcom/android/support/Menu;->featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V

    .line 264
    new-instance v3, Landroid/widget/RelativeLayout;

    invoke-direct {v3, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    .line 265
    .local v3, "titleText":Landroid/widget/RelativeLayout;
    const/4 v4, 0x5

    invoke-virtual {v3, v10, v4, v10, v4}, Landroid/widget/RelativeLayout;->setPadding(IIII)V

    .line 266
    const/16 v10, 0x10

    invoke-virtual {v3, v10}, Landroid/widget/RelativeLayout;->setGravity(I)V

    .line 268
    new-instance v10, Landroid/widget/TextView;

    invoke-direct {v10, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 271
    .local v10, "title":Landroid/widget/TextView;
    sget-object v4, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 273
    iget v4, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 274
    const/high16 v4, 0x41900000    # 18.0f

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTextSize(F)V

    .line 275
    const/16 v4, 0x11

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 277
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v4, v7, v7}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 278
    .local v4, "rl":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v7, 0xe

    invoke-virtual {v4, v7}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 279
    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 283
    new-instance v7, Landroid/widget/TextView;

    invoke-direct {v7, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 284
    .local v7, "subTitle":Landroid/widget/TextView;
    sget-object v14, Landroid/text/TextUtils$TruncateAt;->MARQUEE:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 285
    const/4 v14, -0x1

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setMarqueeRepeatLimit(I)V

    .line 286
    const/4 v14, 0x1

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setSingleLine(Z)V

    .line 287
    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setSelected(Z)V

    .line 288
    iget v14, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setTextColor(I)V

    .line 289
    const/high16 v14, 0x41200000    # 10.0f

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setTextSize(F)V

    .line 290
    const/16 v14, 0x11

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setGravity(I)V

    .line 291
    move-object/from16 v20, v2

    const/4 v2, 0x0

    const/4 v14, 0x5

    .end local v2    # "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    .local v20, "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    invoke-virtual {v7, v2, v2, v2, v14}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 294
    new-instance v2, Landroid/widget/ScrollView;

    invoke-direct {v2, v1}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    .line 296
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    iget v14, v0, Lcom/android/support/Menu;->MENU_HEIGHT:I

    invoke-direct {v0, v14}, Lcom/android/support/Menu;->dp(I)I

    move-result v14

    move-object/from16 v21, v4

    const/4 v4, -0x1

    .end local v4    # "rl":Landroid/widget/RelativeLayout$LayoutParams;
    .local v21, "rl":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-direct {v2, v4, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrlLL:Landroid/widget/LinearLayout$LayoutParams;

    .line 297
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4}, Landroid/widget/LinearLayout;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v4

    invoke-direct {v2, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    .line 298
    iget-object v2, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v4, 0x3f800000    # 1.0f

    iput v4, v2, Landroid/widget/LinearLayout$LayoutParams;->weight:F

    .line 299
    iget-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    sget-boolean v4, Lcom/android/support/Preferences;->isExpanded:Z

    if-eqz v4, :cond_0

    iget-object v4, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    goto :goto_0

    :cond_0
    iget-object v4, v0, Lcom/android/support/Menu;->scrlLL:Landroid/widget/LinearLayout$LayoutParams;

    :goto_0
    invoke-virtual {v2, v4}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 300
    iget-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    iget v4, v0, Lcom/android/support/Menu;->MENU_FEATURE_BG_COLOR:I

    invoke-virtual {v2, v4}, Landroid/widget/ScrollView;->setBackgroundColor(I)V

    .line 301
    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    .line 302
    iget-object v2, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    const/4 v14, 0x1

    invoke-virtual {v2, v14}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 305
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    .line 306
    .local v2, "relativeLayout":Landroid/widget/RelativeLayout;
    const/4 v4, 0x3

    const/16 v14, 0xa

    invoke-virtual {v2, v14, v4, v14, v4}, Landroid/widget/RelativeLayout;->setPadding(IIII)V

    .line 307
    const/16 v4, 0x10

    invoke-virtual {v2, v4}, Landroid/widget/RelativeLayout;->setGravity(I)V

    .line 312
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    const/4 v14, -0x2

    invoke-direct {v4, v14, v14}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 313
    .local v4, "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v14, 0x9

    invoke-virtual {v4, v14}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 315
    new-instance v14, Landroid/widget/Button;

    invoke-direct {v14, v1}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 316
    .local v14, "hideBtn":Landroid/widget/Button;
    invoke-virtual {v14, v4}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 317
    move-object/from16 v18, v4

    const/4 v4, 0x0

    .end local v4    # "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    .local v18, "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-virtual {v14, v4}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 318
    const-string v4, "HIDE"

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 319
    iget v4, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setTextColor(I)V

    .line 320
    new-instance v4, Lcom/android/support/Menu$3;

    invoke-direct {v4, v0}, Lcom/android/support/Menu$3;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 328
    new-instance v4, Lcom/android/support/Menu$4;

    invoke-direct {v4, v0}, Lcom/android/support/Menu$4;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setOnLongClickListener(Landroid/view/View$OnLongClickListener;)V

    .line 338
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    move/from16 v19, v5

    const/4 v5, -0x2

    .end local v5    # "applyDimension":I
    .local v19, "applyDimension":I
    invoke-direct {v4, v5, v5}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 339
    .local v4, "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v5, 0xb

    invoke-virtual {v4, v5}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 341
    new-instance v5, Landroid/widget/Button;

    move-object/from16 v17, v6

    .end local v6    # "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    .local v17, "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v6, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v5, v6}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 342
    .local v5, "closeBtn":Landroid/widget/Button;
    invoke-virtual {v5, v4}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 343
    const/4 v6, 0x0

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 344
    const-string v6, "CLOSE"

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 345
    iget v6, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setTextColor(I)V

    .line 346
    new-instance v6, Lcom/android/support/Menu$5;

    invoke-direct {v6, v0}, Lcom/android/support/Menu$5;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 355
    iget-object v6, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v16, v4

    .end local v4    # "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    .local v16, "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v6, v4}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 356
    iget-object v4, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v6}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 357
    invoke-virtual {v0}, Lcom/android/support/Menu;->IconWebViewData()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1

    .line 358
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v4, v9}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    goto :goto_1

    .line 360
    :cond_1
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v4, v6}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 362
    :goto_1
    invoke-virtual {v3, v10}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 363
    invoke-virtual {v3, v13}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 364
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 365
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 366
    iget-object v4, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    iget-object v6, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v6}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 367
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    invoke-virtual {v4, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 368
    invoke-virtual {v2, v14}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 369
    invoke-virtual {v2, v5}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 370
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 373
    invoke-virtual {v0, v1, v10, v7}, Lcom/android/support/Menu;->Init(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 374
    return-void
.end method

.method private isViewCollapsed()Z
    .locals 1

    .line 1132
    iget-object v0, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v0}, Landroid/widget/RelativeLayout;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method private onTouchListener()Landroid/view/View$OnTouchListener;
    .locals 1

    .line 442
    new-instance v0, Lcom/android/support/Menu$7;

    invoke-direct {v0, p0}, Lcom/android/support/Menu$7;-><init>(Lcom/android/support/Menu;)V

    return-object v0
.end method


# virtual methods
.method native GetFeatureList()[Ljava/lang/String;
.end method

.method native Icon()Ljava/lang/String;
.end method

.method native IconWebViewData()Ljava/lang/String;
.end method

.method native Init(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V
.end method

.method native IsGameLibLoaded()Z
.end method

.method public SetWindowManagerActivity()V
    .locals 8

    .line 420
    new-instance v0, Landroid/view/WindowManager$LayoutParams;

    iget v3, p0, Lcom/android/support/Menu;->POS_X:I

    iget v4, p0, Lcom/android/support/Menu;->POS_Y:I

    .line 425
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x1a

    if-lt v1, v2, :cond_0

    .line 426
    const/16 v1, 0x7f6

    const/16 v5, 0x7f6

    goto :goto_0

    .line 427
    :cond_0
    const/4 v1, 0x2

    const/4 v5, 0x2

    :goto_0
    const v6, 0x800108

    const/4 v7, -0x2

    const/4 v1, -0x2

    const/4 v2, -0x2

    invoke-direct/range {v0 .. v7}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIIIII)V

    iput-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    .line 433
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    const/16 v1, 0x33

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 434
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    iget v1, p0, Lcom/android/support/Menu;->POS_X:I

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 435
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    iget v1, p0, Lcom/android/support/Menu;->POS_Y:I

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 437
    iget-object v0, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    check-cast v0, Landroid/app/Activity;

    invoke-virtual {v0}, Landroid/app/Activity;->getWindowManager()Landroid/view/WindowManager;

    move-result-object v0

    iput-object v0, p0, Lcom/android/support/Menu;->mWindowManager:Landroid/view/WindowManager;

    .line 438
    iget-object v0, p0, Lcom/android/support/Menu;->mWindowManager:Landroid/view/WindowManager;

    iget-object v1, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    iget-object v2, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v0, v1, v2}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 439
    return-void
.end method

.method public SetWindowManagerWindowService()V
    .locals 7

    .line 405
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_0

    const/16 v0, 0x7f6

    goto :goto_0

    :cond_0
    const/16 v0, 0x7d2

    :goto_0
    move v4, v0

    .line 406
    .local v4, "iparams":I
    new-instance v1, Landroid/view/WindowManager$LayoutParams;

    const/16 v5, 0x8

    const/4 v6, -0x3

    const/4 v2, -0x2

    const/4 v3, -0x2

    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    iput-object v1, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    .line 408
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    const/16 v1, 0x33

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 409
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    iget v1, p0, Lcom/android/support/Menu;->POS_X:I

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 410
    iget-object v0, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    iget v1, p0, Lcom/android/support/Menu;->POS_Y:I

    iput v1, v0, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 412
    iget-object v0, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    const-string v1, "window"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/WindowManager;

    iput-object v0, p0, Lcom/android/support/Menu;->mWindowManager:Landroid/view/WindowManager;

    .line 413
    iget-object v0, p0, Lcom/android/support/Menu;->mWindowManager:Landroid/view/WindowManager;

    iget-object v1, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    iget-object v2, p0, Lcom/android/support/Menu;->vmParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v0, v1, v2}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 415
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/android/support/Menu;->overlayRequired:Z

    .line 416
    return-void
.end method

.method native SettingsList()[Ljava/lang/String;
.end method

.method public ShowMenu()V
    .locals 4

    .line 377
    iget-object v0, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    iget-object v1, p0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    invoke-virtual {v0, v1}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 379
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    .line 380
    .local v0, "handler":Landroid/os/Handler;
    new-instance v1, Lcom/android/support/Menu$6;

    invoke-direct {v1, p0, v0}, Lcom/android/support/Menu$6;-><init>(Lcom/android/support/Menu;Landroid/os/Handler;)V

    const-wide/16 v2, 0x1f4

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 400
    return-void
.end method

.method public onDestroy()V
    .locals 2

    .line 1151
    iget-object v0, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    if-eqz v0, :cond_0

    .line 1152
    iget-object v0, p0, Lcom/android/support/Menu;->mWindowManager:Landroid/view/WindowManager;

    iget-object v1, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    invoke-interface {v0, v1}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V

    .line 1154
    :cond_0
    return-void
.end method

.method public setVisibility(I)V
    .locals 1
    .param p1, "view"    # I

    .line 1145
    iget-object v0, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    if-eqz v0, :cond_0

    .line 1146
    iget-object v0, p0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    invoke-virtual {v0, p1}, Landroid/widget/FrameLayout;->setVisibility(I)V

    .line 1148
    :cond_0
    return-void
.end method
