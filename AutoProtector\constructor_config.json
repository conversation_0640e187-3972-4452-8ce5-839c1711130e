{"_comment": "Constructor Protection Configuration", "_description": "Control which constructor types get protected by dex2c", "_usage": "Set to true to ENABLE protection, false to DISABLE protection", "protect_init": false, "_init_description": "Instance constructors (<init>) - Main constructor methods that initialize objects. Set to true to protect <PERSON><PERSON> constructor.", "protect_clinit": false, "_clinit_description": "Static constructors (<clinit>) - Class initialization methods. Usually safer to keep false to avoid class loading issues.", "_notes": ["protect_init: true  = Menu constructor will be converted to native code (recommended for your case)", "protect_init: false = Menu constructor will remain as smali code (current problematic state)", "protect_clinit: false = Static initializers remain as smali (recommended for stability)", "protect_clinit: true = Static initializers converted to native (may cause issues)"], "_troubleshooting": {"if_app_crashes_on_startup": "Set protect_clinit to false", "if_menu_still_shows_too_much_code": "Set protect_init to true", "if_menu_doesnt_work_after_protection": "Set protect_init to false"}}